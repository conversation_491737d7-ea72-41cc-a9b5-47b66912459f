"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[slug]/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-up.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChevronUp; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.460.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronUp = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronUp\", [\n    [\n        \"path\",\n        {\n            d: \"m18 15-6-6-6 6\",\n            key: \"153udz\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-up.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi11cC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQWFBLE1BQU1BLFlBQVlDLGdFQUFnQkEsQ0FBQyxhQUFhO0lBQUM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBa0JDLEtBQUs7UUFBUztLQUFFO0NBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4uLy4uLy4uL3NyYy9pY29ucy9jaGV2cm9uLXVwLnRzPzVmY2EiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uVXBcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE1UZ2dNVFV0TmkwMkxUWWdOaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvY2hldnJvbi11cFxuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXJlYWN0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtKU1guRWxlbWVudH0gSlNYIEVsZW1lbnRcbiAqXG4gKi9cbmNvbnN0IENoZXZyb25VcCA9IGNyZWF0ZUx1Y2lkZUljb24oJ0NoZXZyb25VcCcsIFtbJ3BhdGgnLCB7IGQ6ICdtMTggMTUtNi02LTYgNicsIGtleTogJzE1M3VkeicgfV1dKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2hldnJvblVwO1xuIl0sIm5hbWVzIjpbIkNoZXZyb25VcCIsImNyZWF0ZUx1Y2lkZUljb24iLCJkIiwia2V5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx":
/*!***********************************************************!*\
  !*** ./src/components/blog/ResponsiveTableOfContents.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResponsiveTableOfContents: function() { return /* binding */ ResponsiveTableOfContents; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronUp,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronUp,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,ChevronUp,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* __next_internal_client_entry_do_not_use__ ResponsiveTableOfContents auto */ \nvar _s = $RefreshSig$();\n\n\n\n/**\n * 响应式目录导航组件\n * 根据屏幕尺寸自动调整布局和显示方式\n */ function ResponsiveTableOfContents({ content, className }) {\n    _s();\n    const [tocItems, setTocItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeId, setActiveId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileOpen, setIsMobileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDesktop, setIsDesktop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [readingProgress, setReadingProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hoveredItem, setHoveredItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [previewContent, setPreviewContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [contentAreaBounds, setContentAreaBounds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        right: 0,\n        left: 0\n    });\n    const tocRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 解析内容生成目录 - 增强版本\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const extractHeadings = ()=>{\n            // 优先使用文章内容区域，避免识别评论区域的标题\n            let proseElement = document.querySelector(\".article-content[data-mdx-content]\");\n            if (!proseElement) {\n                // 兼容旧版本，但排除评论区域\n                proseElement = document.querySelector(\"[data-mdx-content]\");\n            }\n            if (!proseElement) return;\n            const headings = proseElement.querySelectorAll(\"h1, h2, h3, h4, h5, h6\");\n            const items = [];\n            headings.forEach((heading, index)=>{\n                // 如果使用兼容模式，排除评论区域的标题\n                if (!proseElement?.classList.contains(\"article-content\")) {\n                    const commentSection = heading.closest(\"[data-comment-section]\");\n                    if (commentSection) return;\n                }\n                const id = heading.id;\n                const text = heading.textContent || \"\";\n                const level = parseInt(heading.tagName.charAt(1));\n                if (id && text) {\n                    // 提取章节预览内容\n                    let preview = \"\";\n                    // 查找下一个标题之前的内容作为预览\n                    const nextHeading = Array.from(headings)[index + 1];\n                    let currentElement = heading.nextElementSibling;\n                    while(currentElement && currentElement !== nextHeading && preview.length < 150){\n                        const textContent = currentElement.textContent || \"\";\n                        if (textContent.trim()) {\n                            preview += textContent.trim() + \" \";\n                        }\n                        currentElement = currentElement.nextElementSibling;\n                    }\n                    // 截断预览内容\n                    if (preview.length > 150) {\n                        preview = preview.substring(0, 150) + \"...\";\n                    }\n                    items.push({\n                        id,\n                        text,\n                        level,\n                        preview: preview.trim()\n                    });\n                }\n            });\n            setTocItems(items);\n            setIsVisible(items.length > 1);\n        };\n        // 立即尝试提取\n        extractHeadings();\n        // 延迟执行，确保DOM已经渲染\n        const timer = setTimeout(extractHeadings, 500);\n        // 监听DOM变化\n        const observer = new MutationObserver(()=>{\n            extractHeadings();\n        });\n        const articleElement = document.querySelector(\"article\");\n        if (articleElement) {\n            observer.observe(articleElement, {\n                childList: true,\n                subtree: true,\n                attributes: true,\n                attributeFilter: [\n                    \"id\"\n                ]\n            });\n        }\n        // 监听自定义刷新事件\n        const handleTocRefresh = ()=>{\n            extractHeadings();\n        };\n        window.addEventListener(\"tocRefresh\", handleTocRefresh);\n        return ()=>{\n            clearTimeout(timer);\n            observer.disconnect();\n            window.removeEventListener(\"tocRefresh\", handleTocRefresh);\n        };\n    }, [\n        content\n    ]);\n    // 监听窗口大小变化\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            setIsDesktop(window.innerWidth >= 1024) // 降低到 lg 断点 (1024px)\n            ;\n            // 计算白色内容区域的边界 - 优先使用文章内容区域\n            let proseElement = document.querySelector(\".article-content[data-mdx-content]\");\n            if (!proseElement) {\n                proseElement = document.querySelector(\"[data-mdx-content]\");\n            }\n            if (proseElement) {\n                const rect = proseElement.getBoundingClientRect();\n                setContentAreaBounds({\n                    right: rect.right,\n                    left: rect.left\n                });\n            }\n        };\n        // 初始设置\n        handleResize();\n        window.addEventListener(\"resize\", handleResize);\n        return ()=>window.removeEventListener(\"resize\", handleResize);\n    }, []);\n    // 监听滚动，更新活跃项\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tocItems.length === 0) return;\n        const handleScroll = ()=>{\n            // 更新内容区域边界（考虑滚动影响）- 优先使用文章内容区域\n            let proseElement = document.querySelector(\".article-content[data-mdx-content]\");\n            if (!proseElement) {\n                proseElement = document.querySelector(\"[data-mdx-content]\");\n            }\n            if (!proseElement) return;\n            const proseRect = proseElement.getBoundingClientRect();\n            const scrollTop = window.scrollY;\n            setContentAreaBounds({\n                right: proseRect.right,\n                left: proseRect.left\n            });\n            // 计算基于文章实际内容的阅读进度\n            const proseTop = proseRect.top + scrollTop;\n            // 找到文章内容的最后一个实际元素（排除空白区域）\n            const lastContentElement = Array.from(proseElement.children).pop();\n            let contentBottom = proseRect.bottom + scrollTop;\n            if (lastContentElement) {\n                const lastElementRect = lastContentElement.getBoundingClientRect();\n                contentBottom = lastElementRect.bottom + scrollTop;\n            }\n            const contentHeight = contentBottom - proseTop;\n            const viewportTop = scrollTop + 200 // 考虑header高度\n            ;\n            let progress = 0;\n            if (contentHeight > 0) {\n                // 当视窗顶部到达或超过内容底部时，进度应该是100%\n                if (viewportTop >= contentBottom - 100) {\n                    progress = 100;\n                } else {\n                    progress = Math.min(100, Math.max(0, (viewportTop - proseTop) / contentHeight * 100));\n                }\n            }\n            setReadingProgress(progress);\n            const headings = tocItems.map((item)=>document.getElementById(item.id)).filter(Boolean);\n            let currentActiveId = \"\";\n            // 改进的活跃状态检测：确保只有在文章内容区域内的标题才会被激活\n            for(let i = 0; i < headings.length; i++){\n                const heading = headings[i];\n                if (heading) {\n                    const headingRect = heading.getBoundingClientRect();\n                    const headingTop = headingRect.top + scrollTop;\n                    // 检查标题是否在文章实际内容区域内\n                    if (headingTop >= proseTop && headingTop <= contentBottom) {\n                        // 标题距离视窗顶部的距离\n                        const distanceFromTop = headingRect.top;\n                        // 只有当标题在合理的视窗位置时才认为是活跃的\n                        if (distanceFromTop <= 300) {\n                            currentActiveId = heading.id;\n                        }\n                    }\n                }\n            }\n            // 如果没有找到活跃标题，使用最后一个在文章内容区域内且已经滚过的标题\n            if (!currentActiveId) {\n                for(let i = headings.length - 1; i >= 0; i--){\n                    const heading = headings[i];\n                    if (heading) {\n                        const headingRect = heading.getBoundingClientRect();\n                        const headingTop = headingRect.top + scrollTop;\n                        if (headingTop >= proseTop && headingTop <= contentBottom && headingRect.top <= 300) {\n                            currentActiveId = heading.id;\n                            break;\n                        }\n                    }\n                }\n            }\n            // 如果还是没有找到，并且用户在文章开始部分，使用第一个标题\n            if (!currentActiveId && scrollTop + 200 < proseTop + 500 && headings.length > 0) {\n                currentActiveId = headings[0]?.id || \"\";\n            }\n            setActiveId(currentActiveId);\n        };\n        // 使用节流优化性能\n        let ticking = false;\n        const throttledHandleScroll = ()=>{\n            if (!ticking) {\n                requestAnimationFrame(()=>{\n                    handleScroll();\n                    ticking = false;\n                });\n                ticking = true;\n            }\n        };\n        // 初始设置活跃项\n        handleScroll();\n        window.addEventListener(\"scroll\", throttledHandleScroll, {\n            passive: true\n        });\n        return ()=>window.removeEventListener(\"scroll\", throttledHandleScroll);\n    }, [\n        tocItems\n    ]);\n    // 滚动到指定标题\n    const scrollToHeading = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((id)=>{\n        const element = document.getElementById(id);\n        if (element) {\n            // 计算更合适的偏移量\n            const headerHeight = 80 // 估算的头部高度\n            ;\n            const extraPadding = 100 // 额外的视觉间距\n            ;\n            const yOffset = -(headerHeight + extraPadding);\n            const elementRect = element.getBoundingClientRect();\n            const elementTop = elementRect.top + window.pageYOffset;\n            let targetY = elementTop + yOffset;\n            // 处理页面末尾的章节 - 确保不会滚动超出页面底部\n            const documentHeight = document.documentElement.scrollHeight;\n            const windowHeight = window.innerHeight;\n            const maxScrollY = documentHeight - windowHeight;\n            // 如果目标位置超出了页面底部，调整到页面底部\n            if (targetY > maxScrollY) {\n                targetY = Math.max(maxScrollY, 0);\n            }\n            // 确保不会滚动到负值\n            targetY = Math.max(targetY, 0);\n            window.scrollTo({\n                top: targetY,\n                behavior: \"smooth\"\n            });\n            setIsMobileOpen(false) // 移动端点击后关闭\n            ;\n        }\n    }, []);\n    // 过滤搜索结果\n    const filteredItems = tocItems.filter((item)=>item.text.toLowerCase().includes(searchQuery.toLowerCase()));\n    // 圆形进度指示器组件\n    const CircularProgress = ({ progress })=>{\n        const radius = 20;\n        const circumference = 2 * Math.PI * radius;\n        const strokeDashoffset = circumference - progress / 100 * circumference;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative w-12 h-12 group/progress\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-12 h-12 transform -rotate-90\",\n                    viewBox: \"0 0 48 48\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"24\",\n                            cy: \"24\",\n                            r: radius,\n                            stroke: \"hsl(var(--muted))\",\n                            strokeWidth: \"3\",\n                            fill: \"none\",\n                            className: \"opacity-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                            cx: \"24\",\n                            cy: \"24\",\n                            r: radius,\n                            stroke: \"hsl(var(--primary))\",\n                            strokeWidth: \"3\",\n                            fill: \"none\",\n                            strokeDasharray: circumference,\n                            strokeDashoffset: strokeDashoffset,\n                            className: \"transition-all duration-300 ease-out drop-shadow-sm\",\n                            strokeLinecap: \"round\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs font-semibold text-primary group-hover/progress:scale-110 transition-transform duration-200\",\n                        children: [\n                            Math.round(progress),\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 scale-110\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n            lineNumber: 316,\n            columnNumber: 7\n        }, this);\n    };\n    // 增强的目录项渲染 - 3D效果和章节预览\n    const renderTocItem = (item)=>{\n        const isActive = activeId === item.id;\n        const isHovered = hoveredItem === item.id;\n        const paddingLeft = (item.level - 1) * 12 + 8;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative group/item\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>scrollToHeading(item.id),\n                    onMouseEnter: ()=>{\n                        setHoveredItem(item.id);\n                        setPreviewContent(item.preview || \"\");\n                    },\n                    onMouseLeave: ()=>{\n                        setHoveredItem(null);\n                        setPreviewContent(\"\");\n                    },\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"w-full text-left text-sm transition-all duration-300 py-3 px-3 rounded-lg hover:bg-muted/50 group relative overflow-hidden hover:scale-105 hover:-translate-y-0.5 hover:shadow-lg\", {\n                        \"text-primary font-medium bg-gradient-to-r from-primary/15 to-primary/5 border-l-4 border-primary shadow-md scale-105\": isActive,\n                        \"text-muted-foreground hover:text-foreground\": !isActive\n                    }),\n                    style: {\n                        paddingLeft: `${paddingLeft}px`,\n                        transform: \"perspective(200px) translateZ(0)\",\n                        transformStyle: \"preserve-3d\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this),\n                        isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary via-primary/80 to-primary/60 rounded-r-sm shadow-sm\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative z-10 space-y-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"block group-hover:text-foreground transition-colors font-medium\",\n                                children: item.text\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this),\n                isHovered && item.preview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute left-full top-0 ml-4 w-80 p-4 bg-card border border-border/50 rounded-xl shadow-xl backdrop-blur-sm z-50 animate-fade-in-up\",\n                    style: {\n                        transform: \"perspective(300px) translateZ(20px)\",\n                        transformStyle: \"preserve-3d\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm font-medium text-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"章节预览\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-muted-foreground leading-relaxed\",\n                                    children: item.preview\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute left-0 top-4 transform -translate-x-1 w-2 h-2 bg-card border-l border-t border-border/50 rotate-45\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 410,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, item.id, true, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n            lineNumber: 363,\n            columnNumber: 7\n        }, this);\n    };\n    if (!isVisible) return null;\n    // 桌面端显示 - 增强3D效果，固定定位优化\n    if (isDesktop) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"transition-all duration-700 ease-in-out transform-gpu\", isCollapsed ? \"w-16\" // 折叠时宽度调整\n             : \"w-full max-h-[80vh]\"),\n            style: {\n                position: \"relative\" // 始终使用相对定位，让目录跟随滚动\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-card/95 backdrop-blur-md border border-border/50 shadow-xl hover:shadow-2xl group/toc overflow-hidden flex flex-col transform-gpu\", isCollapsed ? \"rounded-full w-16 h-16 items-center justify-center cursor-pointer hover:scale-110 hover:shadow-primary/20 absolute transition-all duration-500 ease-out\" : \"rounded-2xl max-h-[80vh] relative transition-all duration-500 ease-out\"),\n                style: isCollapsed ? {\n                    transform: \"perspective(1000px) translateZ(0) translateX(calc(100% - 2rem))\",\n                    transformStyle: \"preserve-3d\",\n                    willChange: \"transform\",\n                    right: \"-2rem\",\n                    width: \"4rem\",\n                    height: \"4rem\"\n                } : {\n                    transform: \"perspective(1000px) translateZ(0)\",\n                    transformStyle: \"preserve-3d\",\n                    willChange: \"transform\",\n                    width: \"100%\",\n                    height: \"auto\"\n                },\n                ref: tocRef,\n                onClick: isCollapsed ? ()=>setIsCollapsed(false) : undefined,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-blue-500/5 opacity-0 group-hover/toc:opacity-100 transition-opacity duration-500 rounded-inherit\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-300\", isCollapsed && \"opacity-100\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"transform transition-all duration-500 ease-out hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircularProgress, {\n                                    progress: readingProgress\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-12 -left-16 bg-card border rounded-lg px-3 py-1 text-xs whitespace-nowrap opacity-0 group-hover/toc:opacity-100 transition-all duration-300 pointer-events-none z-50 shadow-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"目录导航\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-muted-foreground\",\n                                                children: [\n                                                    Math.round(readingProgress),\n                                                    \"% 已读\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 -right-1 transform -translate-y-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"opacity-0 transition-opacity duration-300\", !isCollapsed && \"opacity-100\"),\n                        children: !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative flex items-center justify-between p-4 border-b border-border/30\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CircularProgress, {\n                                                    progress: readingProgress\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                    lineNumber: 499,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-sm font-semibold text-foreground\",\n                                                            children: \"目录导航\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground\",\n                                                            children: [\n                                                                filteredItems.length,\n                                                                \" 个章节\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                    lineNumber: 500,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setIsCollapsed(!isCollapsed),\n                                            className: \"p-2 rounded-lg hover:bg-muted/50 transition-all duration-300 hover:scale-110 group/btn\",\n                                            style: {\n                                                transform: \"perspective(100px) translateZ(5px)\",\n                                                transformStyle: \"preserve-3d\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4 group-hover/btn:scale-110 transition-transform duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                lineNumber: 515,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10 flex flex-col flex-1 min-h-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 pb-2 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"搜索章节...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"w-full pl-10 pr-4 py-2 text-sm border border-border/50 rounded-lg bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all duration-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 overflow-y-auto space-y-2 px-4 pb-4 custom-scrollbar min-h-0\",\n                                            children: filteredItems.length > 0 ? filteredItems.map(renderTocItem) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_ChevronUp_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-8 h-8 mx-auto mb-2 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: \"未找到匹配的章节\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                        lineNumber: 541,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                lineNumber: 449,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n            lineNumber: 440,\n            columnNumber: 7\n        }, this);\n    }\n    // 移动端显示 (md以下)  \n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"md:hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsMobileOpen(true),\n                className: \"fixed bottom-6 right-6 z-40 bg-primary text-primary-foreground p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110\",\n                children: \"\\uD83D\\uDCCB\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                lineNumber: 558,\n                columnNumber: 7\n            }, this),\n            isMobileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm\",\n                onClick: ()=>setIsMobileOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                lineNumber: 567,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed bottom-0 left-0 right-0 z-50 bg-card border-t rounded-t-xl transition-transform duration-300\", isMobileOpen ? \"translate-y-0\" : \"translate-y-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"目录导航\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setIsMobileOpen(false),\n                                    className: \"p-2 rounded-md hover:bg-muted/50\",\n                                    children: \"✕\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"搜索章节...\",\n                                value: searchQuery,\n                                onChange: (e)=>setSearchQuery(e.target.value),\n                                className: \"w-full px-3 py-2 text-sm border border-muted rounded-md bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                                lineNumber: 591,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 590,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-h-80 overflow-y-auto space-y-2 custom-scrollbar\",\n                            children: filteredItems.map(renderTocItem)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                            lineNumber: 600,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/blog/ResponsiveTableOfContents.tsx\",\n        lineNumber: 556,\n        columnNumber: 5\n    }, this);\n}\n_s(ResponsiveTableOfContents, \"cERWELrHFN6wCuLov0025rcIciQ=\");\n_c = ResponsiveTableOfContents;\nvar _c;\n$RefreshReg$(_c, \"ResponsiveTableOfContents\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/layout/BlogLayout.tsx":
/*!**********************************************!*\
  !*** ./src/components/layout/BlogLayout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogLayout: function() { return /* binding */ BlogLayout; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _app_providers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/providers */ \"(app-pages-browser)/./src/app/providers.tsx\");\n/* harmony import */ var _components_layout_Container__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/Container */ \"(app-pages-browser)/./src/components/layout/Container.tsx\");\n/* harmony import */ var _components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/shared/Prose */ \"(app-pages-browser)/./src/components/shared/Prose.tsx\");\n/* harmony import */ var _components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/blog/TableOfContents */ \"(app-pages-browser)/./src/components/blog/TableOfContents.tsx\");\n/* harmony import */ var _components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/blog/ResponsiveTableOfContents */ \"(app-pages-browser)/./src/components/blog/ResponsiveTableOfContents.tsx\");\n/* harmony import */ var _components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/comment/LazyWalineComment */ \"(app-pages-browser)/./src/components/comment/LazyWalineComment.tsx\");\n/* harmony import */ var _components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/comment/CommentStats */ \"(app-pages-browser)/./src/components/comment/CommentStats.tsx\");\n/* harmony import */ var _components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/comment/PageViewCount */ \"(app-pages-browser)/./src/components/comment/PageViewCount.tsx\");\n/* harmony import */ var _lib_formatDate__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/formatDate */ \"(app-pages-browser)/./src/lib/formatDate.ts\");\n/* harmony import */ var _lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/colorSystem */ \"(app-pages-browser)/./src/lib/colorSystem.ts\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Eye,Heart,MessageCircle,Star,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ BlogLayout auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ArrowLeftIcon(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        viewBox: \"0 0 16 16\",\n        fill: \"none\",\n        \"aria-hidden\": \"true\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5\",\n            strokeWidth: \"1.5\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_c = ArrowLeftIcon;\nfunction BlogLayout({ blog, children }) {\n    _s();\n    let router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    let { previousPathname } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_app_providers__WEBPACK_IMPORTED_MODULE_4__.AppContext);\n    const [contentString, setContentString] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(\"\");\n    // 提取内容用于目录生成\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        if (/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().isValidElement(children) && children.props?.content) {\n            setContentString(children.props.content);\n        }\n    }, [\n        children\n    ]);\n    // 监听DOM变化，确保目录能够正确提取\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect(()=>{\n        const timer = setTimeout(()=>{\n            // 触发目录组件重新扫描DOM\n            const event = new CustomEvent(\"tocRefresh\");\n            window.dispatchEvent(event);\n        }, 1000);\n        return ()=>clearTimeout(timer);\n    }, [\n        contentString\n    ]);\n    // 使用与列表页一致的标签样式\n    const getTagStyle = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__.tagStyleGenerator.getTagStyle(color, \"minimal\");\n    const getTagClasses = (color)=>_lib_colorSystem__WEBPACK_IMPORTED_MODULE_13__.tagStyleGenerator.getTagClasses(color, \"minimal\");\n    // 增强的标签悬停效果\n    const getEnhancedTagStyle = (color)=>{\n        const baseStyle = getTagStyle(color);\n        return {\n            ...baseStyle,\n            transition: \"all 0.3s cubic-bezier(0.4, 0, 0.2, 1)\",\n            transform: \"perspective(100px) translateZ(0)\",\n            transformStyle: \"preserve-3d\"\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.ReadingProgress, {}, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Container__WEBPACK_IMPORTED_MODULE_5__.Container, {\n                className: \"mt-8 lg:mt-16 mb-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block lg:hidden absolute right-0 top-0 w-72 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_TableOfContents__WEBPACK_IMPORTED_MODULE_7__.TableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl min-h-0\",\n                            children: [\n                                previousPathname && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: ()=>router.back(),\n                                        \"aria-label\": \"Go back to blogs\",\n                                        className: \"group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ArrowLeftIcon, {\n                                            className: \"h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"article\", {\n                                    className: \"animate-fade-in-up\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                                            className: \"relative mb-12 p-8 rounded-3xl bg-gradient-to-r from-background/80 via-background/90 to-background/80 border border-border/50 backdrop-blur-sm shadow-lg hover:shadow-xl transition-all duration-500 group/header overflow-hidden\",\n                                            style: {\n                                                transform: \"perspective(1000px) translateZ(0)\",\n                                                transformStyle: \"preserve-3d\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative z-10 space-y-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: [\n                                                                blog.is_featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"w-3 h-3 fill-current animate-pulse-soft\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 128,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"Featured\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 127,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                blog.is_major_change && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex items-center px-3 py-1.5 text-xs font-medium bg-gradient-to-r from-orange-500/10 to-red-500/10 border border-orange-500/20 rounded-full text-orange-700 dark:text-orange-300 hover:scale-105 transition-all duration-200 cursor-default\",\n                                                                    children: \"Major Update\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md\",\n                                                            style: {\n                                                                transform: \"translateZ(20px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: blog.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 140,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            style: {\n                                                                transform: \"translateZ(10px)\",\n                                                                transformStyle: \"preserve-3d\"\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 mb-8\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 161,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                    className: \"w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 163,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide\",\n                                                                                    children: \"PRELUDE\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 164,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 168,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 160,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -top-10 -right-10 w-40 h-40 bg-gradient-to-bl from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute -bottom-10 -left-10 w-32 h-32 bg-gradient-to-tr from-primary/8 to-transparent rounded-full blur-3xl opacity-60 group-hover/header:opacity-100 transition-opacity duration-700\",\n                                                                            style: {\n                                                                                transitionDelay: \"0.2s\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 178,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute left-4 top-6 w-1 h-16 bg-gradient-to-b from-primary/60 to-primary/20 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 181,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"relative z-10 pl-6\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-base sm:text-lg leading-relaxed text-slate-700 dark:text-slate-300 group-hover/header:text-slate-900 dark:group-hover/header:text-slate-100 transition-colors duration-300 font-light tracking-wide\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none mr-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 186,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"italic\",\n                                                                                        children: blog.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 187,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xl text-primary/70 font-serif leading-none ml-1\",\n                                                                                        children: '\"'\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                        lineNumber: 188,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 185,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 184,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute bottom-4 right-4 flex items-center gap-1 opacity-60 group-hover/header:opacity-100 transition-opacity duration-300\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-primary/40 rounded-full animate-pulse-soft\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 194,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1.5 h-1.5 bg-primary/30 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.3s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 195,\n                                                                                    columnNumber: 27\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-1 h-1 bg-primary/20 rounded-full animate-pulse-soft\",\n                                                                                    style: {\n                                                                                        animationDelay: \"0.6s\"\n                                                                                    }\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 196,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 193,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 rounded-3xl border border-white/20 dark:border-white/10 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 200,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 172,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap items-center gap-6 text-sm text-muted-foreground\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 209,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"time\", {\n                                                                            dateTime: blog.display_date,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: (0,_lib_formatDate__WEBPACK_IMPORTED_MODULE_12__.formatDate)(blog.display_date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 208,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 217,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: blog.author\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 218,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 216,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 223,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_PageViewCount__WEBPACK_IMPORTED_MODULE_11__.PageViewCount, {\n                                                                            path: `/blogs/${blog.slug}`,\n                                                                            showLabel: false,\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 224,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                blog.likes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-2 group/meta\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                            className: \"w-4 h-4 group-hover/meta:scale-110 group-hover/meta:text-primary transition-all duration-300\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 235,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"group-hover/meta:text-primary transition-colors duration-300\",\n                                                                            children: [\n                                                                                blog.likes,\n                                                                                \" likes\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                            lineNumber: 236,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_CommentStats__WEBPACK_IMPORTED_MODULE_10__.CommentStats, {\n                                                                    path: `/blogs/${blog.slug}`,\n                                                                    showIcons: true\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 243,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        blog.tags && blog.tags.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-wrap gap-2\",\n                                                            children: blog.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    href: `/blogs?tag=${tag.slug || tag.id}`,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_14__.cn)(getTagClasses(tag.color), \"hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 group/tag relative overflow-hidden\"),\n                                                                        style: getEnhancedTagStyle(tag.color),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm\",\n                                                                                style: {\n                                                                                    backgroundColor: tag.color || \"hsl(var(--primary))\"\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 259,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 265,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300\",\n                                                                                children: tag.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 268,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 273,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 251,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, tag.id, false, {\n                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                    lineNumber: 250,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"article-content\",\n                                            \"data-mdx-content\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_Prose__WEBPACK_IMPORTED_MODULE_6__.Prose, {\n                                                className: \"mt-8 mb-0\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                            className: \"mt-12 mb-12\",\n                                            \"data-comment-section\": true,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center mb-6\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-4 backdrop-blur-sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"p-2 rounded-xl bg-primary/10 text-primary\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Eye_Heart_MessageCircle_Star_User_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"w-5 h-5\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 27\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 299,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase\",\n                                                                                children: \"Discussion\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                                lineNumber: 302,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-2xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3\",\n                                                                        children: \"Join the Conversation\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 307,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed\",\n                                                                        children: \"Your thoughts and insights are valuable. Share your perspective and connect with others.\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                        lineNumber: 310,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_comment_LazyWalineComment__WEBPACK_IMPORTED_MODULE_9__.LazyWalineComment, {\n                                                                path: `/blogs/${blog.slug}`,\n                                                                title: blog.title,\n                                                                className: \"max-w-5xl mx-auto\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 pointer-events-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blog_ResponsiveTableOfContents__WEBPACK_IMPORTED_MODULE_8__.ResponsiveTableOfContents, {\n                                content: contentString\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Code/me/My-web/frontend/src/components/layout/BlogLayout.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogLayout, \"/33IpqIXwv7s7HEf4TRQcHGHndA=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c1 = BlogLayout;\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowLeftIcon\");\n$RefreshReg$(_c1, \"BlogLayout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/BlogLayout.tsx\n"));

/***/ })

});