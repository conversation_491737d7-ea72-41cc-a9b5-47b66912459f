'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { cn } from '@/lib/utils'
import { Search, ChevronDown, ChevronUp, BookOpen, Eye, Target, Circle, Menu, X, Minimize2, Maximize2 } from 'lucide-react'

interface TocItem {
  id: string
  text: string
  level: number
  children?: TocItem[]
  preview?: string // 章节预览内容
}

interface ResponsiveTableOfContentsProps {
  content: string
  className?: string
}

// 响应式断点定义 - 红点级设计标准
const RESPONSIVE_BREAKPOINTS = {
  mobile: 768,      // 移动端：底部抽屉
  tablet: 1024,     // 平板：侧边滑出面板
  desktop: 1280,    // 桌面：浮动圆球，智能避让
  large: 1536,      // 大屏：右侧固定，自动折叠
  ultrawide: 1920   // 超宽：右侧固定，完整展开
} as const

// 显示模式类型
type DisplayMode = 'drawer' | 'sidebar' | 'floating' | 'fixed-collapsed' | 'fixed-expanded'

// 获取当前显示模式
const getDisplayMode = (width: number, hasSpace: boolean): DisplayMode => {
  if (width < RESPONSIVE_BREAKPOINTS.mobile) return 'drawer'
  if (width < RESPONSIVE_BREAKPOINTS.tablet) return 'sidebar'
  if (width < RESPONSIVE_BREAKPOINTS.desktop) return hasSpace ? 'floating' : 'sidebar'
  if (width < RESPONSIVE_BREAKPOINTS.large) return 'floating'
  if (width < RESPONSIVE_BREAKPOINTS.ultrawide) return 'fixed-collapsed'
  return 'fixed-expanded'
}

// 智能定位计算
const calculateSafePosition = (contentBounds: { right: number, left: number, width: number }, screenWidth: number) => {
  const minDistance = 24 // 最小安全距离
  const tocWidth = 320 // 目录宽度

  // 计算右侧可用空间
  const rightSpace = screenWidth - contentBounds.right
  const leftSpace = contentBounds.left

  if (rightSpace >= tocWidth + minDistance) {
    // 右侧有足够空间
    return { right: screenWidth - contentBounds.right - tocWidth - minDistance, top: '50%' }
  } else if (leftSpace >= tocWidth + minDistance) {
    // 左侧有足够空间
    return { left: leftSpace - tocWidth - minDistance, top: '50%' }
  } else {
    // 空间不足，使用浮动模式
    return { right: 16, top: '50%' }
  }
}

/**
 * 响应式目录导航组件 - 红点级设计优化版本
 * 智能响应式布局，避免侵入正文内容，提供流畅的用户体验
 */
export function ResponsiveTableOfContents({ content, className }: ResponsiveTableOfContentsProps) {
  const [tocItems, setTocItems] = useState<TocItem[]>([])
  const [activeId, setActiveId] = useState<string>('')
  const [isVisible, setIsVisible] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const [displayMode, setDisplayMode] = useState<DisplayMode>('fixed-expanded')
  const [readingProgress, setReadingProgress] = useState(0)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const [previewContent, setPreviewContent] = useState<string>('')
  const [contentAreaBounds, setContentAreaBounds] = useState({ right: 0, left: 0, width: 0 })
  const [safePosition, setSafePosition] = useState({ right: 16, top: '50%' })
  const [isAnimating, setIsAnimating] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [keyboardMode, setKeyboardMode] = useState(false)
  const tocRef = useRef<HTMLDivElement>(null)

  // 解析内容生成目录 - 增强版本
  useEffect(() => {
    const extractHeadings = () => {
      // 优先使用文章内容区域，避免识别评论区域的标题
      let proseElement = document.querySelector('.article-content[data-mdx-content]')
      if (!proseElement) {
        // 兼容旧版本，但排除评论区域
        proseElement = document.querySelector('[data-mdx-content]')
      }
      if (!proseElement) return

      const headings = proseElement.querySelectorAll('h1, h2, h3, h4, h5, h6')

      const items: TocItem[] = []
      headings.forEach((heading, index) => {
        // 如果使用兼容模式，排除评论区域的标题
        if (!proseElement?.classList.contains('article-content')) {
          const commentSection = heading.closest('[data-comment-section]')
          if (commentSection) return
        }

        const id = heading.id
        const text = heading.textContent || ''
        const level = parseInt(heading.tagName.charAt(1))

        if (id && text) {
          // 提取章节预览内容
          let preview = ''

          // 查找下一个标题之前的内容作为预览
          const nextHeading = Array.from(headings)[index + 1]
          let currentElement = heading.nextElementSibling

          while (currentElement && currentElement !== nextHeading && preview.length < 150) {
            const textContent = currentElement.textContent || ''
            if (textContent.trim()) {
              preview += textContent.trim() + ' '
            }
            currentElement = currentElement.nextElementSibling
          }

          // 截断预览内容
          if (preview.length > 150) {
            preview = preview.substring(0, 150) + '...'
          }

          items.push({
            id,
            text,
            level,
            preview: preview.trim()
          })
        }
      })

      setTocItems(items)
      setIsVisible(items.length > 1)
    }

    // 立即尝试提取
    extractHeadings()

    // 延迟执行，确保DOM已经渲染
    const timer = setTimeout(extractHeadings, 500)

    // 监听DOM变化
    const observer = new MutationObserver(() => {
      extractHeadings()
    })

    const articleElement = document.querySelector('article')
    if (articleElement) {
      observer.observe(articleElement, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ['id']
      })
    }

    // 监听自定义刷新事件
    const handleTocRefresh = () => {
      extractHeadings()
    }

    window.addEventListener('tocRefresh', handleTocRefresh)

    return () => {
      clearTimeout(timer)
      observer.disconnect()
      window.removeEventListener('tocRefresh', handleTocRefresh)
    }
  }, [content])

  // 智能响应式系统 - 红点级设计优化
  useEffect(() => {
    const handleResize = () => {
      const screenWidth = window.innerWidth

      // 计算内容区域边界
      let proseElement = document.querySelector('.article-content[data-mdx-content]')
      if (!proseElement) {
        proseElement = document.querySelector('[data-mdx-content]')
      }

      if (proseElement) {
        const rect = proseElement.getBoundingClientRect()
        const bounds = {
          right: rect.right,
          left: rect.left,
          width: rect.width
        }
        setContentAreaBounds(bounds)

        // 计算是否有足够空间放置目录
        const rightSpace = screenWidth - rect.right
        const hasSpace = rightSpace >= 360 // 目录宽度 + 边距

        // 确定显示模式
        const newMode = getDisplayMode(screenWidth, hasSpace)
        setDisplayMode(newMode)

        // 计算安全位置
        const position = calculateSafePosition(bounds, screenWidth)
        setSafePosition(position)

        // 根据模式自动调整折叠状态
        if (newMode === 'fixed-collapsed') {
          setIsCollapsed(true)
        } else if (newMode === 'fixed-expanded') {
          setIsCollapsed(false)
        }
      }
    }

    // 防抖处理
    let timeoutId: NodeJS.Timeout
    const debouncedResize = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleResize, 100)
    }

    // 初始设置
    handleResize()

    window.addEventListener('resize', debouncedResize)
    return () => {
      window.removeEventListener('resize', debouncedResize)
      clearTimeout(timeoutId)
    }
  }, [])

  // 键盘导航支持 - 红点级无障碍体验
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 检查是否在目录组件内或者按下了目录快捷键
      const isInToc = tocRef.current?.contains(document.activeElement)
      const isGlobalShortcut = e.ctrlKey && e.shiftKey && e.key === 'T'

      if (!isInToc && !isGlobalShortcut) return

      switch (e.key) {
        case 'T':
          if (e.ctrlKey && e.shiftKey) {
            e.preventDefault()
            if (displayMode === 'drawer' || displayMode === 'sidebar') {
              setIsMobileOpen(!isMobileOpen)
            } else {
              setIsCollapsed(!isCollapsed)
            }
            setKeyboardMode(true)
          }
          break

        case 'ArrowDown':
          if (isInToc) {
            e.preventDefault()
            setKeyboardMode(true)
            setSelectedIndex(prev => Math.min(prev + 1, filteredItems.length - 1))
          }
          break

        case 'ArrowUp':
          if (isInToc) {
            e.preventDefault()
            setKeyboardMode(true)
            setSelectedIndex(prev => Math.max(prev - 1, 0))
          }
          break

        case 'Enter':
          if (isInToc && keyboardMode && filteredItems[selectedIndex]) {
            e.preventDefault()
            scrollToHeading(filteredItems[selectedIndex].id)
          }
          break

        case 'Escape':
          if (isInToc) {
            e.preventDefault()
            if (displayMode === 'drawer' || displayMode === 'sidebar') {
              setIsMobileOpen(false)
            }
            setKeyboardMode(false)
            document.activeElement?.blur()
          }
          break

        case '/':
          if (isInToc) {
            e.preventDefault()
            const searchInput = document.querySelector('#toc-search') as HTMLInputElement
            searchInput?.focus()
          }
          break
      }
    }

    const handleMouseMove = () => {
      setKeyboardMode(false)
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('mousemove', handleMouseMove)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('mousemove', handleMouseMove)
    }
  }, [displayMode, isMobileOpen, isCollapsed, filteredItems, selectedIndex, keyboardMode])

  // 监听滚动，更新活跃项和位置
  useEffect(() => {
    if (tocItems.length === 0) return

    const handleScroll = () => {
      // 更新内容区域边界（考虑滚动影响）
      let proseElement = document.querySelector('.article-content[data-mdx-content]')
      if (!proseElement) {
        proseElement = document.querySelector('[data-mdx-content]')
      }
      if (!proseElement) return

      const proseRect = proseElement.getBoundingClientRect()
      const screenWidth = window.innerWidth

      const bounds = {
        right: proseRect.right,
        left: proseRect.left,
        width: proseRect.width
      }
      setContentAreaBounds(bounds)

      // 实时更新安全位置（仅在浮动模式下）
      if (displayMode === 'floating') {
        const position = calculateSafePosition(bounds, screenWidth)
        setSafePosition(position)
      }

      // 计算基于文章实际内容的阅读进度
      const proseTop = proseRect.top + scrollTop
      
      // 找到文章内容的最后一个实际元素（排除空白区域）
      const lastContentElement = Array.from(proseElement.children).pop()
      let contentBottom = proseRect.bottom + scrollTop
      
      if (lastContentElement) {
        const lastElementRect = lastContentElement.getBoundingClientRect()
        contentBottom = lastElementRect.bottom + scrollTop
      }
      
      const contentHeight = contentBottom - proseTop
      const viewportTop = scrollTop + 200 // 考虑header高度
      
      let progress = 0
      if (contentHeight > 0) {
        // 当视窗顶部到达或超过内容底部时，进度应该是100%
        if (viewportTop >= contentBottom - 100) { // 留100px缓冲区
          progress = 100
        } else {
          progress = Math.min(100, Math.max(0, ((viewportTop - proseTop) / contentHeight) * 100))
        }
      }
      setReadingProgress(progress)

      const headings = tocItems.map(item => document.getElementById(item.id)).filter(Boolean)
      let currentActiveId = ''

      // 改进的活跃状态检测：确保只有在文章内容区域内的标题才会被激活
      for (let i = 0; i < headings.length; i++) {
        const heading = headings[i]
        if (heading) {
          const headingRect = heading.getBoundingClientRect()
          const headingTop = headingRect.top + scrollTop
          
          // 检查标题是否在文章实际内容区域内
          if (headingTop >= proseTop && headingTop <= contentBottom) {
            // 标题距离视窗顶部的距离
            const distanceFromTop = headingRect.top
            
            // 只有当标题在合理的视窗位置时才认为是活跃的
            if (distanceFromTop <= 300) { // 标题在视窗顶部300px内
              currentActiveId = heading.id
            }
          }
        }
      }

      // 如果没有找到活跃标题，使用最后一个在文章内容区域内且已经滚过的标题
      if (!currentActiveId) {
        for (let i = headings.length - 1; i >= 0; i--) {
          const heading = headings[i]
          if (heading) {
            const headingRect = heading.getBoundingClientRect()
            const headingTop = headingRect.top + scrollTop
            
            if (headingTop >= proseTop && headingTop <= contentBottom && headingRect.top <= 300) {
              currentActiveId = heading.id
              break
            }
          }
        }
      }

      // 如果还是没有找到，并且用户在文章开始部分，使用第一个标题
      if (!currentActiveId && scrollTop + 200 < proseTop + 500 && headings.length > 0) {
        currentActiveId = headings[0]?.id || ''
      }

      setActiveId(currentActiveId)
    }

    // 使用节流优化性能
    let ticking = false
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll()
          ticking = false
        })
        ticking = true
      }
    }

    // 初始设置活跃项
    handleScroll()

    window.addEventListener('scroll', throttledHandleScroll, { passive: true })
    return () => window.removeEventListener('scroll', throttledHandleScroll)
  }, [tocItems])

  // 滚动到指定标题
  const scrollToHeading = useCallback((id: string) => {
    const element = document.getElementById(id)
    if (element) {
      // 计算更合适的偏移量
      const headerHeight = 80 // 估算的头部高度
      const extraPadding = 100 // 额外的视觉间距
      const yOffset = -(headerHeight + extraPadding)

      const elementRect = element.getBoundingClientRect()
      const elementTop = elementRect.top + window.pageYOffset
      let targetY = elementTop + yOffset

      // 处理页面末尾的章节 - 确保不会滚动超出页面底部
      const documentHeight = document.documentElement.scrollHeight
      const windowHeight = window.innerHeight
      const maxScrollY = documentHeight - windowHeight

      // 如果目标位置超出了页面底部，调整到页面底部
      if (targetY > maxScrollY) {
        targetY = Math.max(maxScrollY, 0)
      }

      // 确保不会滚动到负值
      targetY = Math.max(targetY, 0)

      window.scrollTo({
        top: targetY,
        behavior: 'smooth'
      })
      setIsMobileOpen(false) // 移动端点击后关闭
    }
  }, [])

  // 过滤搜索结果
  const filteredItems = tocItems.filter(item =>
    item.text.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // 圆形进度指示器组件
  const CircularProgress = ({ progress }: { progress: number }) => {
    const radius = 20
    const circumference = 2 * Math.PI * radius
    const strokeDashoffset = circumference - (progress / 100) * circumference

    return (
      <div className="relative w-12 h-12 group/progress">
        <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 48 48">
          {/* 背景圆环 */}
          <circle
            cx="24"
            cy="24"
            r={radius}
            stroke="hsl(var(--muted))"
            strokeWidth="3"
            fill="none"
            className="opacity-20"
          />
          {/* 进度圆环 */}
          <circle
            cx="24"
            cy="24"
            r={radius}
            stroke="hsl(var(--primary))"
            strokeWidth="3"
            fill="none"
            strokeDasharray={circumference}
            strokeDashoffset={strokeDashoffset}
            className="transition-all duration-300 ease-out drop-shadow-sm"
            strokeLinecap="round"
          />
        </svg>

        {/* 中心文字 */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs font-semibold text-primary group-hover/progress:scale-110 transition-transform duration-200">
            {Math.round(progress)}%
          </span>
        </div>

        {/* 悬停光环 */}
        <div className="absolute inset-0 rounded-full bg-primary/10 opacity-0 group-hover/progress:opacity-100 transition-opacity duration-300 scale-110" />
      </div>
    )
  }

  // 增强的目录项渲染 - 红点级交互体验
  const renderTocItem = (item: TocItem, index?: number) => {
    const isActive = activeId === item.id
    const isHovered = hoveredItem === item.id
    const isKeyboardSelected = keyboardMode && typeof index === 'number' && selectedIndex === index
    const paddingLeft = (item.level - 1) * 16 + 12
    const levelColor = `hsl(${(item.level - 1) * 30 + 200}, 70%, 50%)`

    return (
      <div key={item.id} className="relative group/item">
        <button
          onClick={() => {
            scrollToHeading(item.id)
            // 添加点击反馈
            const button = document.getElementById(`toc-item-${item.id}`)
            if (button) {
              button.style.transform = 'scale(0.95)'
              setTimeout(() => {
                button.style.transform = ''
              }, 150)
            }
          }}
          onMouseEnter={() => {
            setHoveredItem(item.id)
            setPreviewContent(item.preview || '')
          }}
          onMouseLeave={() => {
            setHoveredItem(null)
            setPreviewContent('')
          }}
          id={`toc-item-${item.id}`}
          className={cn(
            "w-full text-left text-sm transition-all duration-300 py-3 px-3 rounded-xl group relative overflow-hidden ripple-effect magnetic-effect",
            "hover:bg-gradient-to-r hover:from-primary/10 hover:to-transparent hover:scale-[1.02] hover:-translate-y-0.5 hover:shadow-lg",
            "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-background",
            {
              'text-primary font-semibold bg-gradient-to-r from-primary/20 via-primary/10 to-transparent border border-primary/30 shadow-lg scale-[1.02] shadow-primary/20': isActive,
              'text-muted-foreground hover:text-foreground': !isActive,
              'bg-gradient-to-r from-primary/15 to-primary/5 border border-primary/20 shadow-md': isKeyboardSelected,
            }
          )}
          style={{
            paddingLeft: `${paddingLeft}px`,
            transform: 'perspective(300px) translateZ(0)',
            transformStyle: 'preserve-3d'
          }}
        >
          {/* 层级指示器 */}
          <div
            className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 rounded-full opacity-60 group-hover:opacity-100 transition-opacity"
            style={{ backgroundColor: levelColor }}
          />

          {/* 3D背景效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />

          {/* 活跃状态指示器 - 增强版 */}
          {isActive && (
            <>
              <div className="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-primary via-primary/80 to-primary/60 rounded-r-sm shadow-sm animate-glow-pulse" />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
              </div>
            </>
          )}

          {/* 标题内容 - 增强排版 */}
          <div className="relative z-10 flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <span className="block group-hover:text-foreground transition-colors font-medium truncate pr-2">
                {item.text}
              </span>
              {/* 层级标识 */}
              <div className="flex items-center gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <span className="text-xs text-muted-foreground">H{item.level}</span>
                {item.preview && (
                  <Eye className="w-3 h-3 text-muted-foreground" />
                )}
              </div>
            </div>

            {/* 快速操作按钮 */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  navigator.clipboard.writeText(`#${item.id}`)
                }}
                className="p-1 rounded hover:bg-muted/50 transition-colors"
                title="复制链接"
              >
                <Hash className="w-3 h-3" />
              </button>
            </div>
          </div>

          {/* 悬停光效 - 增强版 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000 rounded-xl" />

          {/* 点击波纹效果 */}
          <div className="absolute inset-0 rounded-xl opacity-0 group-active:opacity-100 bg-primary/10 animate-ping" />
        </button>

        {/* 增强章节预览悬浮卡片 */}
        {isHovered && item.preview && displayMode !== 'drawer' && (
          <div
            className="absolute left-full top-0 ml-4 w-80 p-4 bg-card/95 backdrop-blur-md border border-border/50 rounded-xl shadow-2xl z-50 animate-fade-in card-hover-effect"
            style={{
              transform: 'perspective(400px) translateZ(30px)',
              transformStyle: 'preserve-3d'
            }}
          >
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-primary">
                <BookOpen className="w-4 h-4 animate-glow-pulse" />
                <span>章节预览</span>
                <div className="ml-auto text-xs text-muted-foreground">H{item.level}</div>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {item.preview}
              </p>
              <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t border-border/30">
                <span>点击跳转到此章节</span>
                <span className="text-primary">#{item.id}</span>
              </div>
            </div>

            {/* 3D箭头指示器 */}
            <div
              className="absolute left-0 top-4 transform -translate-x-1 w-2 h-2 bg-card border-l border-t border-border/50 rotate-45"
              style={{ transform: 'translate(-50%, 0) rotateY(-45deg)' }}
            />

            {/* 装饰性光效 */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent rounded-xl pointer-events-none" />
          </div>
        )}
      </div>
    )
  }

  if (!isVisible) return null

  // 根据显示模式渲染不同的UI - 红点级设计优化
  const renderTocContent = () => {
    switch (displayMode) {
      case 'drawer':
        return renderDrawerMode()
      case 'sidebar':
        return renderSidebarMode()
      case 'floating':
        return renderFloatingMode()
      case 'fixed-collapsed':
        return renderFixedCollapsedMode()
      case 'fixed-expanded':
        return renderFixedExpandedMode()
      default:
        return renderFixedExpandedMode()
    }
  }

  // 移动端抽屉模式
  const renderDrawerMode = () => (
    <div className="md:hidden">
      {/* 浮动触发按钮 */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="fixed bottom-6 right-6 w-14 h-14 bg-primary text-primary-foreground rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 z-50 flex items-center justify-center"
      >
        <Menu className="w-6 h-6" />
      </button>

      {/* 抽屉内容 */}
      {isMobileOpen && (
        <>
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 animate-fade-in"
            onClick={() => setIsMobileOpen(false)}
          />
          <div className="fixed bottom-0 left-0 right-0 bg-card border-t rounded-t-2xl shadow-2xl z-50 animate-slide-up max-h-[70vh] overflow-hidden">
            <div className="p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  目录导航
                </h3>
                <button
                  onClick={() => setIsMobileOpen(false)}
                  className="p-2 rounded-md hover:bg-muted/50 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              {renderTocList()}
            </div>
          </div>
        </>
      )}
    </div>
  )

  // 侧边滑出面板模式
  const renderSidebarMode = () => (
    <div className="hidden md:block lg:hidden">
      {/* 触发按钮 */}
      <button
        onClick={() => setIsMobileOpen(true)}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-card border shadow-lg rounded-full hover:shadow-xl transition-all duration-300 hover:scale-105 z-40 flex items-center justify-center"
      >
        <Menu className="w-5 h-5" />
      </button>

      {/* 侧边面板 */}
      {isMobileOpen && (
        <>
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 animate-fade-in"
            onClick={() => setIsMobileOpen(false)}
          />
          <div className="fixed right-0 top-0 bottom-0 w-80 bg-card border-l shadow-2xl z-50 animate-slide-in-right overflow-hidden">
            <div className="p-4 h-full flex flex-col">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <BookOpen className="w-5 h-5" />
                  目录导航
                </h3>
                <button
                  onClick={() => setIsMobileOpen(false)}
                  className="p-2 rounded-md hover:bg-muted/50 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              <div className="flex-1 overflow-hidden">
                {renderTocList()}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )

  // 浮动模式 - 智能避让内容，流体动画
  const renderFloatingMode = () => (
    <div
      className="hidden lg:block xl:hidden fixed z-40 smart-transition animate-smart-position"
      style={{
        right: safePosition.right,
        left: safePosition.left,
        top: safePosition.top,
        transform: 'translateY(-50%)'
      }}
    >
      <div className={cn(
        "smart-transition transform-gpu floating-glow",
        isCollapsed ? "w-14 h-14" : "w-80",
        isAnimating && "animate-morph-circle"
      )}>
        {isCollapsed ? (
          <button
            onClick={() => {
              setIsAnimating(true)
              setTimeout(() => {
                setIsCollapsed(false)
                setIsAnimating(false)
              }, 250)
            }}
            className="w-14 h-14 bg-card/95 backdrop-blur-md border border-border/50 rounded-full shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center group relative overflow-hidden"
          >
            {/* 背景光效 */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full" />

            <CircularProgress progress={readingProgress} size="sm" />

            {/* 悬浮提示 - 增强设计 */}
            <div className="absolute -top-14 -left-20 bg-card/95 backdrop-blur-md border border-border/50 rounded-xl px-4 py-2 text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none shadow-xl z-50">
              <div className="text-center space-y-1">
                <div className="font-medium text-foreground">目录导航</div>
                <div className="text-muted-foreground">{Math.round(readingProgress)}% 已读</div>
                <div className="text-xs text-primary">点击展开</div>
              </div>
              {/* 箭头指示器 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45 -mt-1" />
            </div>

            {/* 点击涟漪效果 */}
            <div className="absolute inset-0 rounded-full opacity-0 group-active:opacity-100 bg-primary/20 animate-ping" />
          </button>
        ) : (
          <div className="bg-card/95 backdrop-blur-md border border-border/50 rounded-2xl shadow-xl max-h-[70vh] overflow-hidden animate-fade-in">
            {/* 头部 - 增强设计 */}
            <div className="p-4 border-b border-border/30 flex items-center justify-between bg-gradient-to-r from-primary/5 to-transparent">
              <h3 className="font-semibold flex items-center gap-2 text-foreground">
                <BookOpen className="w-4 h-4 text-primary" />
                目录导航
              </h3>
              <div className="flex items-center gap-2">
                <CircularProgress progress={readingProgress} size="sm" />
                <span className="text-xs text-muted-foreground font-medium">{Math.round(readingProgress)}%</span>
                <button
                  onClick={() => {
                    setIsAnimating(true)
                    setTimeout(() => {
                      setIsCollapsed(true)
                      setIsAnimating(false)
                    }, 250)
                  }}
                  className="p-1.5 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 group"
                >
                  <Minimize2 className="w-4 h-4 group-hover:text-primary transition-colors" />
                </button>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="max-h-96 overflow-y-auto">
              {renderTocList()}
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // 固定折叠模式 - 增强动画效果
  const renderFixedCollapsedMode = () => (
    <div className="hidden xl:block 2xl:hidden fixed right-4 top-1/2 transform -translate-y-1/2 z-40">
      <div className={cn(
        "smart-transition transform-gpu",
        isCollapsed ? "w-14 h-14" : "w-80",
        isAnimating && (isCollapsed ? "animate-morph-rect" : "animate-morph-circle")
      )}>
        {isCollapsed ? (
          <button
            onClick={() => {
              setIsAnimating(true)
              setTimeout(() => {
                setIsCollapsed(false)
                setIsAnimating(false)
              }, 250)
            }}
            className="w-14 h-14 bg-card/95 backdrop-blur-md border border-border/50 rounded-full shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 flex items-center justify-center group relative overflow-hidden breathing-effect"
          >
            {/* 3D背景光效 */}
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-full" />

            <CircularProgress progress={readingProgress} size="sm" />

            {/* 增强悬浮提示 */}
            <div className="absolute -top-16 -left-24 bg-card/95 backdrop-blur-md border border-border/50 rounded-xl px-4 py-3 text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none shadow-xl z-50">
              <div className="text-center space-y-1">
                <div className="font-medium text-foreground flex items-center gap-1">
                  <BookOpen className="w-3 h-3" />
                  目录导航
                </div>
                <div className="text-muted-foreground">{Math.round(readingProgress)}% 已读</div>
                <div className="text-xs text-primary">点击展开完整目录</div>
              </div>
              {/* 3D箭头指示器 */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-2 h-2 bg-card border-r border-b border-border/50 rotate-45 -mt-1"
                   style={{ transform: 'translate(-50%, -50%) rotateX(45deg)' }} />
            </div>

            {/* 点击波纹效果 */}
            <div className="absolute inset-0 rounded-full opacity-0 group-active:opacity-100 bg-primary/20 animate-ping" />
          </button>
        ) : (
          <div className="bg-card/95 backdrop-blur-md border border-border/50 rounded-2xl shadow-xl max-h-[80vh] overflow-hidden animate-fade-in">
            {/* 增强头部设计 */}
            <div className="p-4 border-b border-border/30 flex items-center justify-between bg-gradient-to-r from-primary/5 to-transparent relative">
              {/* 背景装饰 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/5 to-transparent opacity-50" />

              <h3 className="font-semibold flex items-center gap-2 text-foreground relative z-10">
                <BookOpen className="w-4 h-4 text-primary" />
                目录导航
              </h3>
              <div className="flex items-center gap-3 relative z-10">
                <div className="flex items-center gap-2">
                  <CircularProgress progress={readingProgress} size="sm" />
                  <span className="text-xs text-muted-foreground font-medium">{Math.round(readingProgress)}%</span>
                </div>
                <button
                  onClick={() => {
                    setIsAnimating(true)
                    setTimeout(() => {
                      setIsCollapsed(true)
                      setIsAnimating(false)
                    }, 250)
                  }}
                  className="p-1.5 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 group ripple-effect"
                >
                  <Minimize2 className="w-4 h-4 group-hover:text-primary transition-colors" />
                </button>
              </div>
            </div>

            {/* 内容区域 */}
            <div className="max-h-96 overflow-y-auto">
              {renderTocList()}
            </div>
          </div>
        )}
      </div>
    </div>
  )

  // 固定展开模式 - 最高级体验
  const renderFixedExpandedMode = () => (
    <div className="hidden 2xl:block fixed right-4 top-1/2 transform -translate-y-1/2 w-80 z-40 animate-smart-position">
      <div className="bg-card/95 backdrop-blur-md border border-border/50 rounded-2xl shadow-3xl max-h-[80vh] overflow-hidden card-hover-effect premium-gradient">
        {/* 顶级头部设计 */}
        <div className="p-4 border-b border-border/30 flex items-center justify-between bg-gradient-to-r from-primary/10 via-primary/5 to-transparent relative">
          {/* 动态背景装饰 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/10 to-transparent opacity-50 animate-shimmer" />

          <h3 className="font-semibold flex items-center gap-2 text-foreground relative z-10">
            <div className="relative">
              <BookOpen className="w-4 h-4 text-primary animate-glow-pulse" />
              <div className="absolute inset-0 w-4 h-4 bg-primary/20 rounded-full animate-ping" />
            </div>
            目录导航
          </h3>

          <div className="flex items-center gap-3 relative z-10">
            {/* 增强进度显示 */}
            <div className="flex items-center gap-2 px-2 py-1 rounded-lg bg-muted/30 backdrop-blur-sm">
              <CircularProgress progress={readingProgress} size="sm" />
              <div className="text-xs">
                <div className="font-medium text-foreground">{Math.round(readingProgress)}%</div>
                <div className="text-muted-foreground text-[10px]">已读</div>
              </div>
            </div>

            {/* 功能按钮组 */}
            <div className="flex items-center gap-1">
              <button
                onClick={() => setSearchQuery('')}
                className="p-1.5 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 group"
                title="清除搜索"
              >
                <Search className="w-3 h-3 group-hover:text-primary transition-colors" />
              </button>
              <button
                onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                className="p-1.5 rounded-lg hover:bg-muted/50 transition-all duration-200 hover:scale-105 group"
                title="回到顶部"
              >
                <Target className="w-3 h-3 group-hover:text-primary transition-colors" />
              </button>
            </div>
          </div>
        </div>

        {/* 内容区域 - 增强滚动体验 */}
        <div className="max-h-96 overflow-y-auto custom-scrollbar">
          {renderTocList()}
        </div>

        {/* 底部装饰 */}
        <div className="h-1 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20" />
      </div>
    </div>
  )

  // 通用目录列表渲染 - 增强交互体验
  const renderTocList = () => (
    <div className="p-4">
      {/* 增强搜索框 */}
      <div className="mb-4">
        <div className="relative group">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground group-focus-within:text-primary transition-colors" />
          <input
            id="toc-search"
            type="text"
            placeholder="搜索章节... (按 / 快速聚焦)"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'ArrowDown' && filteredItems.length > 0) {
                e.preventDefault()
                setKeyboardMode(true)
                setSelectedIndex(0)
                const firstItem = document.getElementById(`toc-item-${filteredItems[0].id}`)
                firstItem?.focus()
              }
            }}
            className="w-full pl-10 pr-10 py-2.5 text-sm border border-border/50 rounded-lg bg-background/50 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary/30 transition-all duration-200 custom-focus"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground hover:text-foreground transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          )}
          {/* 搜索框装饰 */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none" />
        </div>
      </div>

      {/* 目录内容 - 增强显示 */}
      <div className="space-y-1 max-h-80 overflow-y-auto custom-scrollbar">
        {filteredItems.length > 0 ? (
          <div className="space-y-1">
            {filteredItems.map((item, index) => (
              <div
                key={item.id}
                className={cn(
                  "animate-fade-in",
                  keyboardMode && selectedIndex === index && "ring-2 ring-primary/50 rounded-xl"
                )}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {renderTocItem(item, index)}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground animate-fade-in">
            <div className="relative">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50 animate-pulse-slow" />
              <div className="absolute inset-0 w-8 h-8 mx-auto bg-primary/10 rounded-full animate-ping" />
            </div>
            <p className="text-sm">
              {searchQuery ? `未找到包含 "${searchQuery}" 的章节` : '暂无目录内容'}
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="mt-2 text-xs text-primary hover:text-primary/80 transition-colors"
              >
                清除搜索条件
              </button>
            )}
          </div>
        )}
      </div>

      {/* 底部统计信息和快捷键提示 */}
      {filteredItems.length > 0 && (
        <div className="mt-3 pt-3 border-t border-border/30 space-y-2">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>共 {filteredItems.length} 个章节</span>
            {searchQuery && (
              <span className="text-primary">搜索结果</span>
            )}
          </div>

          {/* 键盘快捷键提示 */}
          <div className="text-xs text-muted-foreground/70 space-y-1">
            <div className="flex items-center justify-between">
              <span>快捷键:</span>
              <span className="font-mono">Ctrl+Shift+T</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-[10px]">
              <span>↑↓ 导航</span>
              <span>Enter 跳转</span>
              <span>/ 搜索</span>
              <span>Esc 关闭</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )

  return renderTocContent()
}

// 圆形进度指示器组件
const CircularProgress = ({ progress, size = 'md' }: { progress: number, size?: 'sm' | 'md' }) => {
  const radius = size === 'sm' ? 12 : 16
  const strokeWidth = size === 'sm' ? 2 : 3
  const normalizedRadius = radius - strokeWidth * 2
  const circumference = normalizedRadius * 2 * Math.PI
  const strokeDasharray = `${circumference} ${circumference}`
  const strokeDashoffset = circumference - (progress / 100) * circumference

  return (
    <div className={cn("relative", size === 'sm' ? "w-6 h-6" : "w-8 h-8")}>
      <svg
        height={radius * 2}
        width={radius * 2}
        className="transform -rotate-90"
      >
        <circle
          stroke="currentColor"
          fill="transparent"
          strokeWidth={strokeWidth}
          strokeDasharray={strokeDasharray}
          style={{ strokeDashoffset }}
          r={normalizedRadius}
          cx={radius}
          cy={radius}
          className="text-primary transition-all duration-300"
        />
        <circle
          stroke="currentColor"
          fill="transparent"
          strokeWidth={strokeWidth}
          r={normalizedRadius}
          cx={radius}
          cy={radius}
          className="text-muted-foreground/20"
        />
      </svg>
      <div className="absolute inset-0 flex items-center justify-center">
        <BookOpen className={cn("text-primary", size === 'sm' ? "w-3 h-3" : "w-4 h-4")} />
      </div>
    </div>
  )
}
