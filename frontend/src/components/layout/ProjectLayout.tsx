'use client'

import { useContext } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import React from 'react'

import { AppContext } from '@/app/providers'
import { Container } from '@/components/layout/Container'
import { Prose } from '@/components/shared/Prose'
import { ReadingProgress } from '@/components/blog/TableOfContents'
import { ResponsiveTableOfContents } from '@/components/blog/ResponsiveTableOfContents'
import { LazyWalineComment } from '@/components/comment/LazyWalineComment'
import { CommentStats } from '@/components/comment/CommentStats'
import { PageViewCount } from '@/components/comment/PageViewCount'
import { formatDate } from '@/lib/formatDate'
import { tagStyleGenerator } from '@/lib/colorSystem'
import { Calendar, Eye, Heart, User, Star, BookOpen, Code, ExternalLink } from 'lucide-react'
import { cn } from '@/lib/utils'

function ArrowLeftIcon(props: React.ComponentPropsWithoutRef<'svg'>) {
  return (
    <svg viewBox="0 0 16 16" fill="none" aria-hidden="true" {...props}>
      <path
        d="M7.25 11.25 3.75 8m0 0 3.5-3.25M3.75 8h8.5"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}

export function ProjectLayout({
  project,
  children,
}: {
  project: any
  children: React.ReactNode
}) {
  let router = useRouter()
  let { previousPathname } = useContext(AppContext)
  const [contentString, setContentString] = React.useState('')

  // 智能返回处理
  const handleBack = React.useCallback(() => {
    if (previousPathname) {
      // 如果上一页是项目列表或主页，需要刷新以获取最新数据
      const needsRefresh = previousPathname.includes('/projects') || previousPathname === '/'

      router.back()

      if (needsRefresh) {
        // 延迟刷新以确保路由完成
        setTimeout(() => {
          router.refresh()
        }, 150)
      }
    } else {
      // 没有上一页路径，直接跳转到项目列表
      router.push('/projects')
    }
  }, [router, previousPathname])

  // 提取内容用于目录生成
  React.useEffect(() => {
    // 尝试多种方式提取内容
    let extractedContent = ''

    if (React.isValidElement(children) && children.props?.content) {
      extractedContent = children.props.content
    } else if (project.content) {
      extractedContent = project.content
    } else if (project.detail_content) {
      extractedContent = project.detail_content
    }

    setContentString(extractedContent)
  }, [children, project])

  // 监听DOM变化，确保目录能够正确提取
  React.useEffect(() => {
    const timer = setTimeout(() => {
      // 触发目录组件重新扫描DOM
      const event = new CustomEvent('tocRefresh')
      window.dispatchEvent(event)
    }, 1000)

    return () => clearTimeout(timer)
  }, [contentString])

  // 使用与博客页一致的标签样式
  const getTagStyle = (color?: string | null) => tagStyleGenerator.getTagStyle(color, 'minimal')
  const getTagClasses = (color?: string | null) => tagStyleGenerator.getTagClasses(color, 'minimal')

  // 增强的标签悬停效果
  const getEnhancedTagStyle = (color?: string | null) => {
    const baseStyle = getTagStyle(color)
    return {
      ...baseStyle,
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      transform: 'perspective(100px) translateZ(0)',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.1)',
    }
  }

  return (
    <>
      {/* 阅读进度条 */}
      <ReadingProgress />
      
      <Container className="mt-16 lg:mt-32">
        <div className="xl:relative">
          <div className="mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl">
            {/* 返回按钮 */}
            <div className="mb-8">
              <button
                onClick={handleBack}
                className="group flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-white shadow-md shadow-zinc-800/5 ring-1 ring-zinc-900/5 transition hover:shadow-lg hover:scale-105 dark:border dark:border-zinc-700/50 dark:bg-zinc-800 dark:ring-0 dark:ring-white/10 dark:hover:border-zinc-700 dark:hover:ring-white/20"
                aria-label="返回项目列表"
              >
                <ArrowLeftIcon className="h-4 w-4 sm:h-5 sm:w-5 stroke-zinc-500 transition group-hover:stroke-zinc-700 dark:stroke-zinc-500 dark:group-hover:stroke-zinc-400" />
              </button>
            </div>

            {/* 项目头部信息 - 使用博客页面的hero区域设计 */}
            <article className="animate-fade-in-up">
              {/* 增强的项目头部 - 3D卡片效果 */}
              <header
                className="relative mb-16 p-10 rounded-3xl bg-gradient-to-r from-background/85 via-background/95 to-background/85 border border-border/50 backdrop-blur-sm shadow-2xl hover:shadow-3xl hover:shadow-primary/10 transition-all duration-700 group/header overflow-hidden"
                style={{
                  transform: 'perspective(1000px) translateZ(0)',
                  transformStyle: 'preserve-3d',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)'
                }}
              >
                {/* 3D背景效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/header:opacity-100 transition-all duration-500" />
                <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-primary/5 opacity-0 group-hover/header:opacity-100 transition-all duration-700" />

                {/* 边缘高光 */}
                <div className="absolute inset-0 rounded-3xl border border-white/20 opacity-0 group-hover/header:opacity-100 transition-opacity duration-300" />

                {/* 顶部装饰光带 */}
                <div className="absolute top-0 left-1/4 right-1/4 h-0.5 bg-gradient-to-r from-transparent via-primary/60 to-transparent opacity-60" />

                {/* 角落装饰 */}
                <div className="absolute top-4 left-4 w-8 h-8 border-l-2 border-t-2 border-primary/30 rounded-tl-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />
                <div className="absolute top-4 right-4 w-8 h-8 border-r-2 border-t-2 border-primary/30 rounded-tr-lg opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />

                <div className="relative z-10 space-y-6">
                  {/* 状态标签区域 - 增强视觉效果 */}
                  <div className="flex flex-wrap gap-3">
                    {project.is_featured && (
                      <span className="relative inline-flex items-center gap-1.5 px-4 py-2 text-xs font-bold bg-gradient-to-r from-amber-500/15 to-orange-500/15 border-2 border-amber-500/30 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 hover:shadow-lg hover:shadow-amber-500/25 transition-all duration-300 cursor-default overflow-hidden">
                        {/* 背景光效 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-500/20 to-orange-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300" />
                        <Star className="w-3 h-3 fill-current animate-pulse-soft relative z-10" />
                        <span className="relative z-10 tracking-wide">FEATURED</span>
                      </span>
                    )}
                    {project.status && (
                      <span className="relative inline-flex items-center px-4 py-2 text-xs font-bold bg-gradient-to-r from-green-500/15 to-emerald-500/15 border-2 border-green-500/30 rounded-full text-green-700 dark:text-green-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/25 transition-all duration-300 cursor-default overflow-hidden">
                        {/* 背景光效 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-emerald-500/20 opacity-0 hover:opacity-100 transition-opacity duration-300" />
                        <span className="relative z-10 tracking-wide uppercase">{project.status}</span>
                      </span>
                    )}
                  </div>

                  {/* 增强的标题 - 使用更具区分度的字体 */}
                  <h1
                    className="text-4xl sm:text-5xl lg:text-6xl font-black tracking-tight text-foreground leading-tight break-words group-hover/header:text-primary transition-all duration-300 drop-shadow-sm group-hover/header:drop-shadow-md"
                    style={{
                      transform: 'translateZ(20px)',
                      transformStyle: 'preserve-3d',
                      fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif'
                    }}
                  >
                    {project.name}
                  </h1>

                  {/* 项目描述 - 增强的卡片样式 */}
                  {project.description && (
                    <div className="space-y-4">
                      {/* 描述装饰线 */}
                      <div className="flex items-center gap-4 mb-8">
                        <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                        <div className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-full shadow-sm">
                          <BookOpen className="w-4 h-4 text-primary/70 group-hover/header:text-primary group-hover/header:scale-110 transition-all duration-300" />
                          <span className="text-sm font-semibold text-primary/80 group-hover/header:text-primary transition-colors duration-300 tracking-wide">
                            ABSTRACT
                          </span>
                        </div>
                        <div className="h-0.5 bg-gradient-to-r from-transparent via-primary/40 to-transparent flex-1" />
                      </div>

                      {/* 描述内容 */}
                      <div className="relative p-8 sm:p-10 rounded-3xl bg-gradient-to-br from-slate-50/80 via-slate-50/40 to-transparent dark:from-slate-800/40 dark:via-slate-800/20 dark:to-transparent border-2 border-primary/10 backdrop-blur-md group-hover/header:border-primary/25 transition-all duration-500 overflow-hidden shadow-lg shadow-primary/5">
                        {/* 描述背景光效 */}
                        <div className="absolute inset-0 rounded-3xl bg-gradient-to-br from-primary/5 via-primary/2 to-transparent opacity-0 group-hover/header:opacity-100 transition-opacity duration-500" />

                        {/* 装饰性光点 */}
                        <div className="absolute top-6 right-6 w-3 h-3 bg-primary/20 rounded-full blur-sm animate-pulse-soft" />
                        <div className="absolute bottom-6 left-6 w-2 h-2 bg-secondary/30 rounded-full blur-sm animate-pulse-soft" style={{ animationDelay: '1s' }} />

                        {/* 描述文字 - 使用更优雅的字体 */}
                        <div className="relative z-10">
                          <p
                            className="text-lg sm:text-xl leading-relaxed text-muted-foreground group-hover/header:text-foreground transition-colors duration-300 font-medium"
                            style={{
                              fontFamily: 'ui-serif, Georgia, Cambria, "Times New Roman", Times, serif',
                              fontStyle: 'italic'
                            }}
                          >
                            {project.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 元信息区域 - 增强视觉效果 */}
                  <div className="flex flex-wrap items-center gap-8 text-sm text-muted-foreground">
                    {/* 创建日期 */}
                    <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                      <div className="p-1.5 rounded-lg bg-primary/10 text-primary group-hover/meta:bg-primary/20 group-hover/meta:scale-110 transition-all duration-300">
                        <Calendar className="w-4 h-4" />
                      </div>
                      <time dateTime={project.display_date || project.created_at || project.updated_at} className="group-hover/meta:text-primary transition-colors duration-300 font-medium">
                        {formatDate(project.display_date || project.created_at || project.updated_at)}
                      </time>
                    </div>

                    {/* 作者信息 */}
                    {project.author && (
                      <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                        <div className="p-1.5 rounded-lg bg-blue-500/10 text-blue-500 group-hover/meta:bg-blue-500/20 group-hover/meta:scale-110 transition-all duration-300">
                          <User className="w-4 h-4" />
                        </div>
                        <span className="group-hover/meta:text-primary transition-colors duration-300 font-medium">{project.author}</span>
                      </div>
                    )}

                    {/* 浏览量统计 */}
                    <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                      <div className="p-1.5 rounded-lg bg-purple-500/10 text-purple-500 group-hover/meta:bg-purple-500/20 group-hover/meta:scale-110 transition-all duration-300">
                        <Eye className="w-4 h-4" />
                      </div>
                      <PageViewCount
                        path={`/projects/${project.slug}`}
                        showLabel={false}
                        className="group-hover/meta:text-primary transition-colors duration-300 font-medium"
                      />
                    </div>

                    {/* 项目类型 */}
                    {project.module_categories && project.module_categories.length > 0 && (
                      <div className="flex items-center gap-3 group/meta px-4 py-2 rounded-xl bg-gradient-to-r from-slate-50/50 to-slate-100/50 dark:from-slate-800/50 dark:to-slate-900/50 border border-border/30 hover:border-primary/30 transition-all duration-300">
                        <div className="p-1.5 rounded-lg bg-green-500/10 text-green-500 group-hover/meta:bg-green-500/20 group-hover/meta:scale-110 transition-all duration-300">
                          <BookOpen className="w-4 h-4" />
                        </div>
                        <span className="group-hover/meta:text-primary transition-colors duration-300 font-medium">{project.module_categories[0].name}</span>
                      </div>
                    )}

                    {/* Waline评论统计 */}
                    <CommentStats path={`/projects/${project.slug}`} showIcons={true} />
                  </div>

                  {/* 项目链接 */}
                  {(project.github_url || project.demo_url || project.link) && (
                    <div className="flex flex-wrap gap-4">
                      {project.github_url && (
                        <a
                          href={project.github_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link"
                        >
                          <svg className="w-4 h-4 group-hover/link:scale-110 transition-transform duration-300" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                          </svg>
                          GitHub
                        </a>
                      )}
                      {project.demo_url && (
                        <a
                          href={project.demo_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-white bg-gradient-to-r from-primary to-primary/90 rounded-xl hover:shadow-lg hover:shadow-primary/25 hover:scale-105 transition-all duration-300 group/link"
                        >
                          <svg className="w-4 h-4 group-hover/link:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                          Live Demo
                        </a>
                      )}
                      {project.link && (
                        <a
                          href={typeof project.link === 'string' ? project.link : project.link.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 group/link"
                        >
                          <ExternalLink className="w-4 h-4 group-hover/link:scale-110 transition-transform duration-300" />
                          Visit Project
                        </a>
                      )}
                    </div>
                  )}

                  {/* 增强的技术栈标签区域 */}
                  {project.tech_stack && project.tech_stack.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {project.tech_stack.map((tech: any, index: number) => (
                        <span
                          key={index}
                          className="group/tag relative inline-flex items-center px-3 py-1.5 text-sm font-medium text-zinc-700 dark:text-zinc-300 bg-gradient-to-r from-zinc-100 to-zinc-50 dark:from-zinc-800 dark:to-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary/30 dark:hover:border-primary/30 hover:scale-110 hover:-translate-y-1 hover:shadow-lg hover:shadow-primary/20 active:scale-95 transition-all duration-300 cursor-pointer overflow-hidden"
                        >
                          {/* 标签背景光晕 */}
                          <div className="absolute inset-0 opacity-0 group-hover/tag:opacity-20 transition-opacity duration-300 blur-sm bg-primary" />

                          {/* 标签闪光效果 */}
                          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent translate-x-[-100%] group-hover/tag:translate-x-[100%] transition-transform duration-500" />

                          {/* 标签内容 */}
                          <span className="relative z-10 group-hover/tag:text-shadow-sm transition-all duration-300">
                            {tech.name || tech}
                          </span>

                          {/* 点击涟漪效果 */}
                          <div className="absolute inset-0 opacity-0 group-active/tag:opacity-30 bg-white rounded-md scale-0 group-active/tag:scale-100 transition-all duration-200" />
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </header>

              {/* 项目内容 */}
              <Prose className="mt-8" data-mdx-content>
                {children}
              </Prose>

              {/* Waline评论区域 - Red Dot Award Design */}
              <section className="mt-20 mb-12">
                <div className="relative">
                  {/* 装饰性分割线 */}
                  <div className="absolute left-0 right-0 top-0 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
                  <div className="absolute left-1/4 right-1/4 top-0 h-px bg-gradient-to-r from-transparent via-primary/40 to-transparent" />
                  
                  <div className="pt-12">
                    {/* 优雅的标题设计 */}
                    <div className="text-center mb-12">
                      <div className="inline-flex items-center justify-center p-3 rounded-2xl bg-gradient-to-r from-primary/5 via-primary/10 to-primary/5 border border-primary/10 mb-6 backdrop-blur-sm">
                        <div className="p-2 rounded-xl bg-primary/10 text-primary">
                          <Code className="w-5 h-5" />
                        </div>
                        <span className="ml-3 text-sm font-semibold text-primary/80 tracking-wide uppercase">
                          Project Discussion
                        </span>
                      </div>
                      <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground bg-clip-text text-transparent mb-3">
                        Share Your Experience
                      </h2>
                      <p className="text-muted-foreground text-sm max-w-lg mx-auto leading-relaxed">
                        Questions, suggestions, or just want to share how you&apos;re using this project? Let&apos;s connect.
                      </p>
                    </div>

                    <LazyWalineComment
                      path={`/projects/${project.slug}`}
                      title={project.name}
                      className="max-w-5xl mx-auto"
                    />
                  </div>
                </div>
              </section>
            </article>
          </div>

          {/* 智能响应式目录导航 - 红点级设计优化 */}
          <ResponsiveTableOfContents content={contentString} />
        </div>
      </Container>
    </>
  )
}
